#!/usr/bin/env python3
"""
Systematic refactoring script to replace y20acb421 with internal_y20acb421
in appropriate contexts while preserving database schema references.

This script analyzes Python files and replaces y20acb421 with internal_y20acb421
only in contexts where it refers to internal variables, function parameters,
and business logic, while preserving database column references and API contracts.
"""

import os
import re
import sys
from pathlib import Path

def should_skip_file(file_path):
    """Determine if a file should be skipped"""
    skip_patterns = [
        '/migrations/',
        '/__pycache__/',
        '.pyc',
        '.pyo',
        '/tests/',  # We'll handle tests separately if needed
        '/docker-',
        'Dockerfile',
        '.git/',
        '.md',
        '.json',
        '.yml',
        '.yaml'
    ]
    
    for pattern in skip_patterns:
        if pattern in str(file_path):
            return True
    return False

def should_replace_in_context(line, line_num, file_content_lines):
    """
    Determine if y20acb421 in this line should be replaced based on context.
    
    Returns True if it should be replaced (internal variable/parameter),
    False if it should be preserved (database column reference).
    """
    line_lower = line.lower().strip()
    
    # Skip if it's a database column reference in SQL
    sql_indicators = [
        'select ', 'from ', 'where ', 'join ', 'on ', 'insert into',
        'update ', 'delete ', 'order by', 'group by', 'having ',
        'y20acb421 =', '= y20acb421', 'y20acb421,', ',y20acb421',
        'y20acb421)', '(y20acb421', 'y20acb421 ', ' y20acb421'
    ]
    
    # Check if this line contains SQL-like syntax
    for indicator in sql_indicators:
        if indicator in line_lower:
            # This is likely a database query - preserve y20acb421 references
            return False
    
    # Skip migration files completely
    if 'migration' in str(file_content_lines) or 'alembic' in str(file_content_lines):
        return False
    
    # Skip if it's setting a database field directly
    if '.y20acb421 =' in line or '.y20acb421=' in line:
        return False
        
    # Skip if it's accessing a database field attribute  
    if '.y20acb421' in line and not ('def ' in line or 'async def' in line):
        return False
    
    # Skip if it appears to be a dictionary key for API response
    if '"y20acb421"' in line or "'y20acb421'" in line:
        return False
    
    # Replace if it's a function parameter
    if ('def ' in line or 'async def' in line) and 'y20acb421:' in line:
        return True
    
    # Replace if it's a variable assignment
    if 'y20acb421 =' in line and 'WHERE' not in line.upper():
        return True
    
    # Replace if it's in a function call parameter
    if 'y20acb421=' in line and not any(sql in line_lower for sql in sql_indicators):
        return True
    
    # Replace if it's in logging that references internal variables
    if 'logger.' in line and 'y20acb421' in line and '{' in line:
        return True
    
    return False

def refactor_file(file_path):
    """Refactor a single Python file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            lines = content.split('\n')
        
        modified_lines = []
        changes_made = 0
        
        for i, line in enumerate(lines):
            if 'y20acb421' in line:
                if should_replace_in_context(line, i, lines):
                    # Replace y20acb421 with internal_y20acb421 in this context
                    # Use word boundaries to avoid partial matches
                    new_line = re.sub(r'\by20acb421\b', 'internal_y20acb421', line)
                    if new_line != line:
                        changes_made += 1
                        print(f"  Line {i+1}: {line.strip()}")
                        print(f"  ->      {new_line.strip()}")
                    modified_lines.append(new_line)
                else:
                    modified_lines.append(line)
            else:
                modified_lines.append(line)
        
        if changes_made > 0:
            # Write back the modified content
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(modified_lines))
            print(f"✅ {file_path}: {changes_made} changes made")
            return changes_made
        else:
            print(f"⏭️  {file_path}: No changes needed")
            return 0
            
    except Exception as e:
        print(f"❌ Error processing {file_path}: {e}")
        return 0

def main():
    """Main refactoring function"""
    base_path = Path("/Users/<USER>/Desktop/ROZANA/rozana-oms-service/application")
    
    if not base_path.exists():
        print(f"❌ Base path does not exist: {base_path}")
        sys.exit(1)
    
    print("🔧 Starting systematic y20acb421 -> internal_y20acb421 refactoring...")
    print(f"📁 Base path: {base_path}")
    print()
    
    total_files = 0
    total_changes = 0
    
    # Find all Python files
    for file_path in base_path.rglob("*.py"):
        if should_skip_file(file_path):
            continue
            
        total_files += 1
        print(f"\n📄 Processing: {file_path}")
        changes = refactor_file(file_path)
        total_changes += changes
    
    print(f"\n🎉 Refactoring complete!")
    print(f"📊 Files processed: {total_files}")
    print(f"🔄 Total changes made: {total_changes}")
    
    if total_changes > 0:
        print(f"\n⚠️  Please review the changes and run tests to ensure functionality is preserved.")

if __name__ == "__main__":
    main()
