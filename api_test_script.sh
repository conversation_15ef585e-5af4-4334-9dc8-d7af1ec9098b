#!/bin/bash

# Comprehensive API Testing Script
# Date: $(date)
# Environment: Docker
# Authentication: Firebase Tokens

echo "🚀 COMPREHENSIVE API TESTING REPORT"
echo "===================================="
echo "📅 Date: $(date)"
echo "🐳 Environment: Docker"
echo "🔑 Authentication: Firebase Tokens (App & POS)"
echo ""

# Set tokens (replace with actual tokens)
APP_TOKEN="eyJhbGciOiJSUzI1NiIsImtpZCI6IjkyZTg4M2NjNDY2M2E2MzMyYWRhNmJjMWU0N2YzZmY1ZTRjOGI1ZDciLCJ0eXAiOiJKV1QifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.TsPjP8neQXbx3zB7oBk4A26jaXRcJI0ouihNC83gRdt_WUXw-t-w9rlzz9GZIQyygt_7YfvyHUcuv_Il2Bf3CKGrLR0w8J1UGip08kQUWwxmeF6xRNkDURvPy1xW0xX_X0iv1-JIhKnore3cAfTjiaxLEy9qRpSb4V7sxgDonZ7OxVY-v9CS5cxDitPL_CMw5mOaZKLQJGmDbVUyDMfotJeJNj8zbvI0wIZ-tnfZp_QImE_lBzyoS8TdFZy3y66QrUfXkjvR0aofVvW8-p76t6i_Zjunym9xDTdkea84aoKfeuDRSZuucH8uqEKevPz3w14XEw-Wekx95WaK_3-pWg"

POS_TOKEN="eyJhbGciOiJSUzI1NiIsImtpZCI6IjkyZTg4M2NjNDY2M2E2MzMyYWRhNmJjMWU0N2YzZmY1ZTRjOGI1ZDciLCJ0eXAiOiJKV1QifQ.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.MKs6NQuzgId08uxOuoVGj-yDB1IralBDjRj7kJm3rI4Fv4rA23X48VZHeJ1Zhmqq-DHoLR45UY1du5hK5WiLGoezIQBS_GnC11xTpuYoR6ODbm9GhdEIhMDUOQjeXknmDX4RDUAozmHU_UI8-9pOF0drpebMGGGsOPylSO8UQxc1ak2fIvyZgMNXOCKzf09CnU07GdfD44JYJboPzCUEjewReqYLpsUc_el7xgXXqC6M86Cc4WsrMR7ZhyIG6D8Ae557-SQM5FRZOnr3L2jIoWqZHkC-VulZqjFFPofJMBLPqkV0bFm9rM7fBH8XB_2ZfSbQU8TwtjeD37RuR0AlZw"

BASE_URL="http://localhost:8000"
SAMPLE_ORDER_ID="OMS-DEV-240814-09590001"

# Function to test API endpoint
test_api() {
    local test_name="$1"
    local method="$2"
    local endpoint="$3"
    local token="$4"
    local data="$5"
    
    echo "Testing: $test_name"
    echo "Endpoint: $method $endpoint"
    
    if [ "$method" = "GET" ]; then
        response=$(curl -s -w "HTTPSTATUS:%{http_code}" "$endpoint" -H "Authorization: $token")
    else
        response=$(curl -s -w "HTTPSTATUS:%{http_code}" -X "$method" "$endpoint" -H "Authorization: $token" -H "Content-Type: application/json" -d "$data")
    fi
    
    http_code=$(echo $response | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
    response_body=$(echo $response | sed -e 's/HTTPSTATUS\:.*//g')
    
    if [ $http_code -eq 200 ] || [ $http_code -eq 201 ]; then
        echo "✅ Status: SUCCESS (HTTP $http_code)"
    else
        echo "❌ Status: FAILED (HTTP $http_code)"
    fi
    
    echo "Response: $(echo $response_body | jq -c . 2>/dev/null || echo $response_body | head -c 200)"
    echo "---"
    echo ""
}

# Health Check
echo "=== HEALTH CHECK ==="
test_api "Health Check" "GET" "$BASE_URL/health" "" ""

# APP APIs
echo "=== APP API TESTS (Firebase App Token) ==="

test_api "Get All Orders" "GET" "$BASE_URL/app/v1/orders?page_size=5&page=1&sort_order=desc" "$APP_TOKEN" ""

test_api "Get Order Details" "GET" "$BASE_URL/app/v1/order_details?order_id=$SAMPLE_ORDER_ID" "$APP_TOKEN" ""

test_api "Create Order Again" "POST" "$BASE_URL/app/v1/create_order_again" "$APP_TOKEN" '{"order_id": "'$SAMPLE_ORDER_ID'"}'

test_api "Create Payment Record" "POST" "$BASE_URL/app/v1/create_payment_record" "$APP_TOKEN" '{"order_id": "'$SAMPLE_ORDER_ID'", "payment_amount": 100.50, "payment_mode": "razorpay", "payment_status": "pending"}'

test_api "Update Payment with Razorpay Details" "POST" "$BASE_URL/app/v1/update_payment_with_razorpay_details" "$APP_TOKEN" '{"order_id": "'$SAMPLE_ORDER_ID'", "razorpay_order_id": "order_test_123", "razorpay_payment_id": "pay_test_456"}'

test_api "Get Payment Details for Order" "GET" "$BASE_URL/app/v1/get_payment_details_for_order?order_id=$SAMPLE_ORDER_ID" "$APP_TOKEN" ""

test_api "Create Return" "POST" "$BASE_URL/app/v1/create_return" "$APP_TOKEN" '{"order_id": "'$SAMPLE_ORDER_ID'", "return_type": "full", "return_reason": "damaged", "items": [{"sku": "test-sku-001", "quantity": 1}]}'

test_api "Get Payment Status for Order" "GET" "$BASE_URL/app/v1/get_payment_status_for_order?order_id=$SAMPLE_ORDER_ID" "$APP_TOKEN" ""

test_api "Process Wallet Payment" "POST" "$BASE_URL/app/v1/process_wallet_payment" "$APP_TOKEN" '{"order_id": "'$SAMPLE_ORDER_ID'", "amount": 50.25}'

test_api "Confirm Wallet Payment" "POST" "$BASE_URL/app/v1/confirm_wallet_payment" "$APP_TOKEN" '{"payment_id": "test_payment_123", "order_id": "'$SAMPLE_ORDER_ID'"}'

test_api "Confirm Wallet Payments and Update Order" "POST" "$BASE_URL/app/v1/confirm_wallet_payments_and_update_order" "$APP_TOKEN" '{"order_id": "'$SAMPLE_ORDER_ID'"}'

test_api "Add Wallet Entry" "POST" "$BASE_URL/app/v1/add_wallet_entry" "$APP_TOKEN" '{"order_id": "'$SAMPLE_ORDER_ID'", "amount": 25.75, "transaction_type": "credit"}'

# POS APIs
echo "=== POS API TESTS (Firebase POS Token) ==="

test_api "Update Order Status" "POST" "$BASE_URL/pos/v1/update_order_status" "$POS_TOKEN" '{"order_id": "'$SAMPLE_ORDER_ID'", "status": "confirmed"}'

test_api "Update Item Status" "POST" "$BASE_URL/pos/v1/update_item_status" "$POS_TOKEN" '{"order_id": "'$SAMPLE_ORDER_ID'", "sku": "test-sku-001", "status": "packed"}'

test_api "Get Facility Name" "GET" "$BASE_URL/pos/v1/get_facility_name?order_id=$SAMPLE_ORDER_ID" "$POS_TOKEN" ""

test_api "Get Return Eligible Items" "GET" "$BASE_URL/pos/v1/get_return_eligible_items?order_id=$SAMPLE_ORDER_ID" "$POS_TOKEN" ""

test_api "Search Orders by Customer" "GET" "$BASE_URL/pos/v1/search_orders_by_customer?customer_id=test_customer_123" "$POS_TOKEN" ""

# Token Validation APIs
echo "=== TOKEN VALIDATION APIs ==="

test_api "Get Orders (Token Validation)" "GET" "$BASE_URL/orders" "$APP_TOKEN" ""

test_api "Validate App Token" "POST" "$BASE_URL/validate-app-token" "$APP_TOKEN" ""

test_api "Validate POS Token" "POST" "$BASE_URL/validate-pos-token" "$POS_TOKEN" ""

echo "🏁 API TESTING COMPLETED"
echo "========================"
echo "📊 Summary: Check individual test results above"
echo "📋 Report generated at: $(date)"
