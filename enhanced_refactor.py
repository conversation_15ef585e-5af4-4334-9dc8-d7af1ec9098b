#!/usr/bin/env python3
"""
Enhanced refactoring script to complete the remaining y20acb421 -> internal_y20acb421 transformations.
This script specifically targets function parameters and internal variable usage.
"""

import os
import re
from pathlib import Path

def update_function_signatures_and_bodies():
    """Update remaining function signatures and their internal usages"""
    
    # Files that need function signature updates
    files_to_update = [
        "/Users/<USER>/Desktop/ROZANA/rozana-oms-service/application/app/services/payment_service.py",
        "/Users/<USER>/Desktop/ROZANA/rozana-oms-service/application/app/services/order_service.py",
        "/Users/<USER>/Desktop/ROZANA/rozana-oms-service/application/app/services/order_query_service.py",
        "/Users/<USER>/Desktop/ROZANA/rozana-oms-service/application/app/core/order_return.py",
        "/Users/<USER>/Desktop/ROZANA/rozana-oms-service/application/app/core/order_cancel.py"
    ]
    
    total_changes = 0
    
    for file_path in files_to_update:
        if not os.path.exists(file_path):
            continue
            
        print(f"\n📄 Processing: {file_path}")
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Target patterns for function signatures and their bodies
        patterns = [
            # Function parameters in signatures
            (r'(\s+)(y20acb421: (?:str|int))([,\)])', r'\1internal_y20acb421: \3\4'),
            (r'(async def \w+\([^)]*)(y20acb421: (?:str|int))([,\)])', r'\1internal_y20acb421: \3\4'),
            (r'(def \w+\([^)]*)(y20acb421: (?:str|int))([,\)])', r'\1internal_y20acb421: \3\4'),
            
            # Function calls with y20acb421 parameter 
            (r'(\w+\(.*?)(y20acb421=)([^,\)]+)', r'\1internal_y20acb421=\3'),
            
            # Variable assignments (not database fields)
            (r'^(\s*)(y20acb421\s*=)([^"])', r'\1internal_y20acb421 =\3'),
            
            # In f-strings and logging
            (r'(f"[^"]*)(y20acb421=)(\{[^}]+\})', r'\1internal_y20acb421=\3'),
            (r'(f"[^"]*\{)(y20acb421)(\})', r'\1internal_y20acb421\3'),
            
            # In function documentation
            (r'(\s+y20acb421:)', r'\1internal_y20acb421:'),
            
            # In variable usage within functions (be more careful)
            (r'(\s+)(y20acb421)(\s*[,\)])(?![=\s]*=)', r'\1internal_y20acb421\3'),
        ]
        
        changes_in_file = 0
        
        for pattern, replacement in patterns:
            new_content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
            if new_content != content:
                changes_count = len(re.findall(pattern, content, flags=re.MULTILINE))
                changes_in_file += changes_count
                content = new_content
                print(f"  Applied pattern: {pattern[:50]}... -> {changes_count} changes")
        
        # Manual specific replacements for edge cases
        specific_replacements = [
            # Update function calls that use positional y20acb421
            ('await self.get_payments_for_order(y20acb421)', 'await self.get_payments_for_order(internal_y20acb421)'),
            ('get_payments_for_order(y20acb421)', 'get_payments_for_order(internal_y20acb421)'),
            
            # Update logger calls that reference y20acb421 variables  
            ('f"Checking payment completion for order {y20acb421}"', 'f"Checking payment completion for order {internal_y20acb421}"'),
            ('f"No payments found for order {y20acb421}"', 'f"No payments found for order {internal_y20acb421}"'),
            ('f"Order {y20acb421} has incomplete', 'f"Order {internal_y20acb421} has incomplete'),
            ('f"Order {y20acb421} still has incomplete', 'f"Order {internal_y20acb421} still has incomplete'),
            ('f"All payments completed and order {y20acb421}', 'f"All payments completed and order {internal_y20acb421}'),
            ('f"Error checking payment completion for order {y20acb421}', 'f"Error checking payment completion for order {internal_y20acb421}'),
            
            # Fix y20acb421 references in conditions and assignments
            ('y20acb421)', 'internal_y20acb421)'),
            ('(y20acb421', '(internal_y20acb421'),
            ('y20acb421,', 'internal_y20acb421,'),
            (',y20acb421', ',internal_y20acb421'),
        ]
        
        for old, new in specific_replacements:
            if old in content:
                content = content.replace(old, new)
                changes_in_file += 1
                print(f"  Specific replacement: {old} -> {new}")
        
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ {changes_in_file} changes made")
            total_changes += changes_in_file
        else:
            print(f"⏭️  No changes needed")
    
    return total_changes

if __name__ == "__main__":
    print("🔧 Enhanced refactoring for remaining y20acb421 references...")
    total = update_function_signatures_and_bodies()
    print(f"\n🎉 Enhanced refactoring complete! Total changes: {total}")
