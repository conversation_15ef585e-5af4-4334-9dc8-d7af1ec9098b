/* Modern Glassy Rozana Payment Test Client */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    background: #f8fafc;
    min-height: 100vh;
    color: #334155;
    margin: 0;
    padding: 0;
}

/* Main Layout - Horizontal Split */
.main-layout {
    display: flex;
    min-height: 100vh;
    background: #f8fafc;
}

/* Content Area - 70% */
.content-area {
    flex: 0 0 70%;
    overflow-y: auto;
    background: #f8fafc;
}

/* Logs Panel - 30% */
.logs-panel {
    flex: 0 0 30%;
    background: #1e293b;
    border-left: 1px solid #334155;
    display: flex;
    flex-direction: column;
    transition: all 0.3s ease;
}

.logs-panel.collapsed {
    flex: 0 0 40px;
}

/* App Container */
.app-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 40px 20px;
    position: relative;
    z-index: 1;
}

/* Header */
.app-header {
    text-align: center;
    color: #1e293b;
    margin-bottom: 40px;
    position: relative;
    background: white;
    border-radius: 8px;
    padding: 30px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.logo {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo i {
    font-size: 2.5rem;
    color: #1e40af;
}

.logo h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0;
    color: #1e293b;
}

.subtitle {
    font-size: 1.1rem;
    color: #64748b;
    font-weight: 400;
    margin-top: 8px;
}

.settings-btn {
    background: #f1f5f9;
    border: 1px solid #cbd5e1;
    border-radius: 8px;
    padding: 12px;
    color: #475569;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 1.2rem;
}

.settings-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
}

/* Journey Progress */
.journey-progress {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 40px;
    padding: 0 20px;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    opacity: 0.4;
    transition: all 0.3s ease;
}

.step.active {
    opacity: 1;
}

.step.completed {
    opacity: 1;
}

.step-circle {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: #e2e8f0;
    border: 2px solid #cbd5e1;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    color: #475569;
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.step.active .step-circle {
    background: #3b82f6;
    border-color: #1d4ed8;
    color: white;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    transform: scale(1.1);
}

.step.completed .step-circle {
    background: #22c55e;
    border-color: #16a34a;
    color: white;
    box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3);
}

.step span {
    color: #374151;
    font-weight: 500;
    font-size: 0.9rem;
}

.step.active span {
    color: #1e40af;
    font-weight: 600;
}

.step.completed span {
    color: #16a34a;
    font-weight: 600;
}

.step-line {
    width: 80px;
    height: 2px;
    background: #cbd5e1;
    margin: 0 20px;
}

/* Professional Card */
.glass-card {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 32px;
    margin-bottom: 24px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
}

.glass-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 25px 50px rgba(0,0,0,0.15);
    border-color: rgba(255, 255, 255, 0.3);
}

.card-header {
    text-align: center;
    margin-bottom: 30px;
}

.card-header i {
    font-size: 3rem;
    color: #1e40af;
    margin-bottom: 15px;
}

.card-header h2 {
    color: #1e293b;
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 10px;
}

.card-header p {
    color: #64748b;
    font-size: 1.1rem;
    font-weight: 400;
}

.card-header.success i {
    color: #4CAF50;
}

/* Journey Steps */
.journey-step {
    display: none;
    margin-bottom: 30px;
}

.journey-step.active {
    display: block;
    animation: fadeInUp 0.5s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Form Styles */
.order-form {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-group label {
    display: flex;
    align-items: center;
    gap: 12px;
    font-weight: 500;
    color: #374151;
    font-size: 0.95rem;
    margin-bottom: 12px;
}

.form-group label i {
    font-size: 1rem;
    opacity: 0.8;
    width: 16px;
    text-align: center;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 12px 16px;
    background: #f8fafc;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    color: #374151;
    transition: all 0.2s ease;
    font-family: inherit;
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: #9ca3af;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    background: white;
}

.amount-group input {
    font-size: 1.2rem;
    font-weight: 600;
    text-align: center;
}

.section-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin: 30px 0 20px 0;
    padding-bottom: 12px;
    border-bottom: 1px solid #e2e8f0;
}

.section-header i {
    font-size: 1.3rem;
    color: #1e40af;
}

.section-header h3 {
    color: #1e293b;
    font-size: 1.3rem;
    font-weight: 600;
    margin: 0;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.item-row {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr auto;
    gap: 10px;
    margin-bottom: 10px;
    align-items: center;
}

.item-row input {
    margin-bottom: 0;
}

/* Buttons */
.journey-btn {
    background: #1e40af;
    border: 1px solid #1d4ed8;
    color: white;
    padding: 16px 32px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    min-width: 160px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.journey-btn:hover {
    background: #1d4ed8;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.journey-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.journey-btn.secondary {
    background: #64748b;
    border-color: #475569;
}

.journey-btn.payment-btn {
    background: linear-gradient(135deg, #1e40af 0%, #1d4ed8 100%);
    border-color: rgba(59, 130, 246, 0.5);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.journey-btn.payment-btn:hover {
    box-shadow: 0 12px 35px rgba(59, 130, 246, 0.4);
}

.add-item-btn {
    background: #f1f5f9;
    border: 1px dashed #cbd5e1;
    color: #475569;
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-top: 15px;
}

.add-item-btn:hover {
    background: #e2e8f0;
    border-style: solid;
    color: #374151;
    border-color: #94a3b8;
}

.form-actions {
    display: flex;
    justify-content: center;
    margin-top: 30px;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background: white;
    border: 1px solid #e2e8f0;
    margin: 5% auto;
    padding: 0;
    border-radius: 12px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px 30px;
    border-bottom: 1px solid #e2e8f0;
    background: #f8fafc;
    border-radius: 12px 12px 0 0;
}

.modal-header h2 {
    color: #1e293b;
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 12px;
}

.modal-header i {
    color: #1e40af;
}

.close {
    color: #64748b;
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s ease;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    background: #e2e8f0;
}

.close:hover {
    color: #374151;
    background: #cbd5e1;
}

.modal-body {
    padding: 30px;
}

.save-btn {
    background: linear-gradient(135deg, #1e40af 0%, #1d4ed8 100%);
    color: white;
    border: none;
    padding: 14px 28px;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    width: 100%;
    margin-top: 20px;
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.save-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba(59, 130, 246, 0.4);
}

button[type="button"]:not(.primary-btn):not(.secondary-btn) {
    background: #dc3545;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
}

button[type="button"]:not(.primary-btn):not(.secondary-btn):hover {
    background: #c82333;
}

#orderDetails {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    border-left: 4px solid #667eea;
}

#orderDetails h3 {
    color: #333;
    margin-bottom: 10px;
}

#orderDetails p {
    margin-bottom: 5px;
    color: #666;
}

/* Logs Panel Header */
.logs-header {
    background: #334155;
    padding: 16px 20px;
    border-bottom: 1px solid #475569;
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.logs-header:hover {
    background: #3f4b5f;
}

.logs-header h3 {
    color: #e2e8f0;
    font-size: 1rem;
    font-weight: 600;
    margin: 0;
    flex: 1;
}

.logs-header i {
    color: #94a3b8;
}

.toggle-icon {
    transition: transform 0.3s ease;
}

.logs-panel:not(.collapsed) .toggle-icon {
    transform: rotate(90deg);
}

/* Logs Content */
.logs-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    transition: all 0.3s ease;
}

.logs-panel.collapsed .logs-content {
    display: none;
}

#logs {
    background: #0f172a;
    color: #22c55e;
    padding: 16px;
    font-family: 'Courier New', monospace;
    font-size: 13px;
    flex: 1;
    overflow-y: auto;
    white-space: pre-wrap;
    line-height: 1.4;
}

.logs-actions {
    padding: 12px 16px;
    background: #334155;
    border-top: 1px solid #475569;
    flex-shrink: 0;
}

.logs-btn {
    background: #dc2626;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
}

.logs-btn:hover {
    background: #b91c1c;
}

/* Enhanced Payment Status Styles */
.status-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
    padding: 20px;
    border-radius: 12px;
    border-left: 5px solid #28a745;
    margin-bottom: 20px;
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.15);
    border: 1px solid #c3e6cb;
}

.status-error {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
    padding: 20px;
    border-radius: 12px;
    border-left: 5px solid #dc3545;
    margin-bottom: 20px;
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.15);
    border: 1px solid #f5c6cb;
}

.status-pending {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
    padding: 20px;
    border-radius: 12px;
    border-left: 5px solid #ffc107;
    margin-bottom: 20px;
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.15);
    border: 1px solid #ffeaa7;
}

/* Status Header */
.status-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.status-icon {
    font-size: 2rem;
    line-height: 1;
}

.status-header h3 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
}

/* Status Summary */
.status-summary {
    margin-bottom: 20px;
}

.status-summary p {
    margin: 8px 0;
    font-size: 0.95rem;
}

/* Payment Counts */
.payment-counts {
    display: flex;
    gap: 15px;
    margin-top: 15px;
    flex-wrap: wrap;
}

.count-item {
    background: rgba(255, 255, 255, 0.7);
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.count-item.completed {
    background: rgba(40, 167, 69, 0.1);
    color: #155724;
    border-color: #28a745;
}

.count-item.pending {
    background: rgba(255, 193, 7, 0.1);
    color: #856404;
    border-color: #ffc107;
}

.count-item.failed {
    background: rgba(220, 53, 69, 0.1);
    color: #721c24;
    border-color: #dc3545;
}

/* Payments List */
.payments-list {
    margin-top: 20px;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    padding-top: 20px;
}

/* Split Payment Styles */
.split-payment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.payment-method {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    position: relative;
    transition: all 0.2s ease;
}

.payment-method:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.payment-method-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.payment-method-title {
    font-weight: 600;
    color: #1e293b;
    display: flex;
    align-items: center;
    gap: 8px;
}

.remove-payment-method {
    background: none;
    border: none;
    color: #ef4444;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.remove-payment-method:hover {
    background: #fee2e2;
}

.payment-method-fields {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.payment-method-amount {
    grid-column: 1 / -1;
}

.payment-summary {
    background: #f8fafc;
    border-radius: 8px;
    padding: 15px;
    margin-top: 15px;
    border: 1px solid #e2e8f0;
}

.payment-summary > div {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
}

.payment-summary .payment-total {
    font-weight: 600;
    font-size: 1.1em;
    color: #1e40af;
}

.payment-summary .payment-remaining {
    color: #64748b;
}

#paymentError {
    color: #ef4444;
    margin-top: 10px;
    font-size: 0.9em;
    display: none;
}

.payment-method-select {
    position: relative;
}

.payment-method-select select {
    padding-right: 40px;
}

.payment-method-select i {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #64748b;
    pointer-events: none;
}

.payment-item {
    background: rgba(255, 255, 255, 0.8);
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.payment-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.payment-item.payment-success {
    border-left: 4px solid #28a745;
    background: rgba(212, 237, 218, 0.3);
}

.payment-item.payment-pending {
    border-left: 4px solid #ffc107;
    background: rgba(255, 243, 205, 0.3);
}

/* Payment Header */
.payment-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.payment-icon {
    font-size: 1.2rem;
    margin-right: 8px;
}

.payment-status {
    font-weight: 600;
    flex: 1;
}

.payment-amount {
    font-size: 1.1rem;
    font-weight: 700;
    color: #1e40af;
}

/* Payment Details */
.payment-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 8px;
}

.payment-details p {
    margin: 4px 0;
    font-size: 0.9rem;
    color: #64748b;
}

.payment-details strong {
    color: #334155;
}

.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.hidden {
    display: none;
}

@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .item-row {
        grid-template-columns: 1fr;
        gap: 5px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .card {
        padding: 20px;
    }
}
