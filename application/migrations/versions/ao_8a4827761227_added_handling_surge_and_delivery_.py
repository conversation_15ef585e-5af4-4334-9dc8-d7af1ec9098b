"""Added handling, surge and delivery charges

Revision ID: 8a4827761227
Revises: 44c28dc9f0b9
Create Date: 2025-08-23 13:02:47.282080

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '8a4827761227'
down_revision: Union[str, Sequence[str], None] = '44c28dc9f0b9'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('orders', sa.Column('handling_charge', sa.DECIMAL(precision=10, scale=2), server_default='0.00', nullable=False))
    op.add_column('orders', sa.Column('surge_charge', sa.DECIMAL(precision=10, scale=2), server_default='0.00', nullable=False))
    op.add_column('orders', sa.Column('delivery_charge', sa.DECIMAL(precision=10, scale=2), server_default='0.00', nullable=False))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('orders', 'delivery_charge')
    op.drop_column('orders', 'surge_charge')
    op.drop_column('orders', 'handling_charge')
    # ### end Alembic commands ###
