"""added invoice details

Revision ID: b5cfbf7e306b
Revises: e35eb039516c
Create Date: 2025-08-19 17:24:42.880611

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'b5cfbf7e306b'
down_revision: Union[str, Sequence[str], None] = 'e35eb039516c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('invoice_details',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('invoice_number', sa.String(length=128), nullable=False),
        sa.Column('order_id', sa.Integer(), nullable=False),
        sa.Column('invoice_s3_url', sa.String(length=2048), nullable=True),
        sa.Column('created_at', sa.TIMESTAMP(timezone=True), nullable=False),
        sa.Column('updated_at', sa.TIMESTAMP(timezone=True), nullable=False),
        sa.ForeignKeyConstraint(['order_id'], ['orders.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_invoice_details_invoice_number', 'invoice_details', ['invoice_number'], unique=False)
    op.create_index(op.f('ix_invoice_details_id'), 'invoice_details', ['id'], unique=False)
    op.create_index(op.f('ix_invoice_details_invoice_number'), 'invoice_details', ['invoice_number'], unique=False)
    op.create_index(op.f('ix_invoice_details_order_id'), 'invoice_details', ['order_id'], unique=False)
    op.drop_column('orders', 'invoice_key')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('orders', sa.Column('invoice_key', sa.VARCHAR(length=1024), autoincrement=False, nullable=True))
    op.drop_index(op.f('ix_invoice_details_order_id'), table_name='invoice_details')
    op.drop_index(op.f('ix_invoice_details_invoice_number'), table_name='invoice_details')
    op.drop_index(op.f('ix_invoice_details_id'), table_name='invoice_details')
    op.drop_index('idx_invoice_details_invoice_number', table_name='invoice_details')
    op.drop_table('invoice_details')
    # ### end Alembic commands ###
