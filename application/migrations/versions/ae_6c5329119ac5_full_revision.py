"""full revision

Revision ID: 6c5329119ac5
Revises: 0be2f20fe890
Create Date: 2025-08-13 06:36:57.024843

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '6c5329119ac5'
down_revision: Union[str, Sequence[str], None] = '0be2f20fe890'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('order_addresses', sa.Column('longitude', sa.DECIMAL(precision=10, scale=7), nullable=True))
    op.add_column('order_addresses', sa.Column('latitude', sa.DECIMAL(precision=10, scale=7), nullable=True))
    op.create_index('idx_order_addresses_coordinates', 'order_addresses', ['longitude', 'latitude'], unique=False)
    op.add_column('order_items', sa.Column('pack_uom_quantity', sa.Integer(), nullable=False))
    op.add_column('order_items', sa.Column('wh_sku', sa.String(length=255), nullable=True))
    op.add_column('orders', sa.Column('invoice_key', sa.String(length=1024), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('orders', 'invoice_key')
    op.drop_column('order_items', 'wh_sku')
    op.drop_column('order_items', 'pack_uom_quantity')
    op.drop_index('idx_order_addresses_coordinates', table_name='order_addresses')
    op.drop_column('order_addresses', 'latitude')
    op.drop_column('order_addresses', 'longitude')
    # ### end Alembic commands ###
