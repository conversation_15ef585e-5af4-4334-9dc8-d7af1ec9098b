"""added raven link

Revision ID: 44c28dc9f0b9
Revises: 939452d84aee
Create Date: 2025-08-19 17:46:44.717291

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '44c28dc9f0b9'
down_revision: Union[str, Sequence[str], None] = '939452d84aee'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('invoice_details', sa.Column('raven_link', sa.String(length=2048), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('invoice_details', 'raven_link')
    # ### end Alembic commands ###
