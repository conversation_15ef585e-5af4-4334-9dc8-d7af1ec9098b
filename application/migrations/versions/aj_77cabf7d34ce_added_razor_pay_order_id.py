"""added razor pay order id

Revision ID: 77cabf7d34ce
Revises: 6e3b2cbde808
Create Date: 2025-08-18 18:59:50.497168

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '77cabf7d34ce'
down_revision: Union[str, Sequence[str], None] = '6e3b2cbde808'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('payment_details', sa.Column('payment_order_id', sa.String(length=50), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('payment_details', 'payment_order_id')
    # ### end Alembic commands ###
