from fastapi import APIRouter, HTTPException
from app.dto.returns import CreateReturnRequest  # type: ignore
from app.core.order_return import create_return_core

app_router = APIRouter(tags=["app"])

@app_router.post("/create_return")
async def create_return(req: CreateReturnRequest):
    # Data Extraction
    items_to_return = [{"sku": i.sku, "quantity": i.quantity} for i in (req.items or [])]
    order_full_return = bool(getattr(req, "order_full_return", False))
    order_id = req.order_id
    return_reason = req.return_reason

    # Core Business Logic
    try:
        return await create_return_core(
            order_id=order_id,
            items=items_to_return,
            order_full_return=order_full_return,
            return_reason=return_reason,
        )
    except HTTPException:
        # Let FastAPI render HTTP errors without converting to 500
        raise
    except Exception as e:
        # Unexpected errors only
        raise HTTPException(status_code=500, detail="Internal server error")