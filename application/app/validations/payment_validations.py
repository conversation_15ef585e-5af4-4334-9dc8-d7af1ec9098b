from app.dto.orders import OrderCreate


class PaymentValidator:
    def __init__(self, order: OrderCreate = None):
        self.order = order

    def validate_payment_mode_by_origin(self, origin: str):
        """Validate all payment modes based on origin (app/pos)."""
        # Payment object is now mandatory, so we can directly access it
        payment_modes = [p.payment_mode.lower() for p in self.order.payment]

        if origin == "app":
            # App origin: only 'cod' and 'razorpay' are allowed
            allowed = {"cod", "razorpay", "wallet"}
            for mode in payment_modes:
                if mode not in allowed:
                    raise ValueError(f"For app origin, payment_mode must be one of: {', '.join(allowed)}. Got: {mode}")
        elif origin == "pos":
            # POS origin: 'cash', 'cash_and_online', and 'razorpay' are allowed
            allowed = {"cash", "razorpay", "wallet"}
            for mode in payment_modes:
                if mode not in allowed:
                    raise ValueError(f"For pos origin, payment_mode must be one of: {', '.join(allowed)}. Got: {mode}")
        # For other origins, no specific restrictions (or add as needed)

    def validate_create_payment_order(self, origin: str):
        """Validate create_payment_order for each payment dict."""
        for payment in self.order.payment:
            # Wallet must always have create_payment_order=True
            if payment.payment_mode.lower() == "wallet" and not payment.create_payment_order:
                raise ValueError("For wallet payments, create_payment_order must be true")
            if not payment.create_payment_order:
                continue
            if origin not in ["app", "pos"]:
                raise ValueError(f"create_payment_order is only valid for 'app' or 'pos' origins. Got: {origin}")
            valid_modes = {"razorpay", "wallet"}
            if payment.payment_mode.lower() not in valid_modes:
                raise ValueError(
                    f"create_payment_order is only valid for payment modes: {', '.join(valid_modes)}. "
                    f"Got: {payment.payment_mode}"
                )

    def validate_payment_amounts(self):
        """Validate that payment amounts match the total order amount."""
        if not self.order.payment:
            return
            
        total_payment_amount = 0
        for payment in self.order.payment:
            if payment.amount is None:
                raise ValueError(f"Payment amount is required for payment mode: {payment.payment_mode}")
            total_payment_amount += payment.amount
        
        # Check if payment amounts sum equals total_amount
        if abs(total_payment_amount - self.order.total_amount) > 0.01:  # Allow small floating point differences
            raise ValueError(
                f"Payment amounts sum ({total_payment_amount}) does not match order total amount ({self.order.total_amount})"
            )

    def validate_payment_combinations(self, origin: str):
        """Validate payment combinations to prevent duplicates and ensure valid combinations."""
        if not self.order.payment:
            return
            
        # Count each payment mode
        payment_mode_counts = {}
        for payment in self.order.payment:
            mode = payment.payment_mode.lower()
            payment_mode_counts[mode] = payment_mode_counts.get(mode, 0) + 1
        
        # Check for duplicate payment modes
        for mode, count in payment_mode_counts.items():
            if count > 1:
                raise ValueError(f"Duplicate payment mode '{mode}' found. Each payment mode can only appear once.")
        
        # Validate payment mode combinations
        payment_modes = set(payment_mode_counts.keys())
        
        # Check for invalid combinations based on origin
        if origin == "app":
            # App origin: only allow razorpay + wallet combination or single payment modes
            if len(payment_modes) > 2:
                raise ValueError("App origin: Maximum 2 payment modes allowed.")
            elif len(payment_modes) == 2:
                if not (payment_modes == {"razorpay", "wallet"}):
                    raise ValueError("App origin: Only 'razorpay' + 'wallet' combination is allowed.")
            # Single payment mode is always valid for app
        elif origin == "pos":
            # POS origin: allow all combinations
            pass
        else:
            # Default validation for other origins
            if len(payment_modes) == 1:
                # Single payment mode is valid
                pass
            else:
                raise ValueError("Invalid payment combination. Allowed: single payment mode or 'cash' + 'razorpay' combination.")

    def validate_payment_configuration(self, origin: str):
        """Comprehensive validation for payment configuration based on origin"""
        self.validate_payment_mode_by_origin(origin)
        self.validate_create_payment_order(origin)
        self.validate_payment_combinations(origin)
        self.validate_payment_amounts()
