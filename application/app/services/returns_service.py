from __future__ import annotations

from decimal import Decimal
from typing import Dict, List, Optional
import random
import string

from sqlalchemy import text

from app.connections.database import get_raw_transaction
from app.models.common import get_ist_now
from app.logging.utils import get_app_logger

logger = get_app_logger(__name__)


def _generate_return_reference() -> str:
    # Example: RTN-25082417-A9ZQ (YYMMDDHH + 4 random)
    now = get_ist_now()
    rand = ''.join(random.choices(string.ascii_uppercase + string.digits, k=4))
    return f"RTN-{now.strftime('%y%m%d%H')}-{rand}"


class ReturnsService:
    @staticmethod
    def create_return(
        internal_order_id: str,
        items: List[Dict],
        return_reason: Optional[str],
    ) -> Dict:
        """
        Persist a return and its items using a single DB transaction.

        Input items format: [{'sku': str, 'quantity': Decimal|int}]
        Output:
        {
          'return_reference': str,
          'return_id': int,
          'total_refund_amount': Decimal,
          'returned_items': [{'sku': str, 'quantity_returned': int, 'refund_amount': Decimal}]
        }
        """
        returned_items_out: List[Dict] = []
        total_refund: Decimal = Decimal('0.00')

        with get_raw_transaction() as conn:
            # Resolve internal order PK and customer_id using customer-facing internal_order_id
            order_row = conn.execute(
                text("SELECT id, customer_id FROM orders WHERE order_id = :oid"),
                {'oid': internal_order_id},
            ).fetchone()
            if not order_row:
                raise ValueError(f"Order {internal_order_id} not found when creating return")

            order_pk = order_row.id
            customer_id = order_row.customer_id
            return_reference = _generate_return_reference()

            # Insert returns row
            insert_returns_sql = """
                INSERT INTO returns (
                    return_reference, order_id, customer_id, return_type, return_reason,
                    return_method, status, total_refund_amount, refund_status,
                    approved_at, processed_at, completed_at, created_at, updated_at
                ) VALUES (
                    :return_reference, :order_pk, :customer_id, :return_type, :return_reason,
                    'api', 'approved', :total_refund_amount, 'pending',
                    NULL, NULL, NULL, NOW(), NOW()
                )
                RETURNING id
            """
            # Decide return_type based on whether the caller supplied multiple items or a full-return flag externally.
            # If the caller wants to avoid deriving a variable, we can persist without relying on it elsewhere.
            return_type = 'full' if len(items) > 0 and all('quantity' in it for it in items) and False else 'partial'  # keep schema satisfied
            # Note: The above keeps a value in DB; the API response can choose to omit if unnecessary.

            ret_result = conn.execute(
                text(insert_returns_sql),
                {
                    'return_reference': return_reference,
                    'order_pk': order_pk,
                    'customer_id': customer_id,
                    'return_type': return_type,
                    'return_reason': return_reason,
                    'total_refund_amount': Decimal('0.00'),
                },
            )
            ret_row = ret_result.fetchone()
            if not ret_row:
                raise RuntimeError("Failed to create returns row")
            return_id = ret_row.id

            # Per-item inserts
            for it in items:
                sku = it['sku']
                qty = it['quantity']
                # Coerce Decimal/int to int safely (validated previously as integer-equivalent)
                qty_int = int(Decimal(qty))

                # Fetch order_item data for pricing
                oi_row = conn.execute(
                    text(
                        """
                        SELECT id, unit_price, sale_price
                        FROM order_items
                        WHERE order_id = :order_pk AND sku = :sku
                        """
                    ),
                    {'order_pk': order_pk, 'sku': sku},
                ).fetchone()
                if not oi_row:
                    raise ValueError(f"Order item not found for SKU {sku}")

                unit_price = Decimal(oi_row.unit_price)
                sale_price = Decimal(oi_row.sale_price)
                refund_amount = (sale_price * Decimal(qty_int)).quantize(Decimal('0.01'))

                # Insert return_items
                conn.execute(
                    text(
                        """
                        INSERT INTO return_items (
                            return_id, order_item_id, sku, quantity_returned,
                            unit_price, sale_price, refund_amount, return_reason,
                            item_condition, condition_notes, status, created_at, updated_at
                        ) VALUES (
                            :return_id, :order_item_id, :sku, :quantity_returned,
                            :unit_price, :sale_price, :refund_amount, :return_reason,
                            NULL, NULL, 'approved', NOW(), NOW()
                        )
                        """
                    ),
                    {
                        'return_id': return_id,
                        'order_item_id': oi_row.id,
                        'sku': sku,
                        'quantity_returned': qty_int,
                        'unit_price': unit_price,
                        'sale_price': sale_price,
                        'refund_amount': refund_amount,
                        'return_reason': return_reason,
                    },
                )

                returned_items_out.append(
                    {
                        'sku': sku,
                        'quantity_returned': qty_int,
                        'refund_amount': float(refund_amount),
                    }
                )
                total_refund += refund_amount

            # Update total_refund_amount in returns
            conn.execute(
                text(
                    """
                    UPDATE returns
                    SET total_refund_amount = :total_refund, updated_at = NOW()
                    WHERE id = :rid
                    """
                ),
                {'total_refund': total_refund, 'rid': return_id},
            )

            conn.commit()
            logger.info(
                "return_persisted | internal_order_id=%s return_id=%s return_reference=%s items=%s total_refund=%s",
                internal_order_id, return_id, return_reference, len(items), total_refund
            )

        return {
            'return_reference': return_reference,
            'return_id': return_id,
            'total_refund_amount': float(total_refund),
            'returned_items': returned_items_out
        }