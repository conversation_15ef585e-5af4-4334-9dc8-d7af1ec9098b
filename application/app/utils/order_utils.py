from app.core.constants import OrderStatus


def can_cancel_order(current_status: int) -> bool:
    if current_status in [OrderStatus.CANCELED, OrderStatus.CANCELLED_PENDING_REFUND]:
        return False

    can_cancel = [
        OrderStatus.DRAFT,           # 0
        OrderStatus.OPEN,            # 10
        OrderStatus.WMS_SYNCED,      # 21
        OrderStatus.WMS_SYNC_FAILED, # 22
        OrderStatus.WMS_OPEN,        # 23
        OrderStatus.WMS_INPROGRESS   # 24
    ]

    return current_status in can_cancel
