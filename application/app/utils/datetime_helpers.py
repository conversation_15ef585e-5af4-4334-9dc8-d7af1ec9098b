"""
Utility functions for date and time formatting.
"""

from datetime import datetime, timezone
from typing import Optional


def format_datetime_readable(dt: Optional[datetime]) -> Optional[str]:
    """
    Format datetime object to human readable format.
    
    Args:
        dt: datetime object to format
        
    Returns:
        Formatted string like "January 15, 2024 at 2:30 PM" or None if dt is None
    """
    if dt is None:
        return None
    
    # Keep the datetime in its original timezone (don't convert to system timezone)
    # This preserves IST timezone when IST datetime is passed
    return dt.strftime("%B %d, %Y at %I:%M %p")


def format_timestamp_readable(timestamp: Optional[int]) -> Optional[str]:
    """
    Convert Unix timestamp to human readable format.
    
    Args:
        timestamp: Unix timestamp (seconds since epoch)
        
    Returns:
        Formatted string like "January 15, 2024 at 2:30 PM" or None if timestamp is None
    """
    if timestamp is None:
        return None
    # Convert UTC timestamp to local timezone
    dt = datetime.fromtimestamp(timestamp, tz=timezone.utc).astimezone()
    return format_datetime_readable(dt)
