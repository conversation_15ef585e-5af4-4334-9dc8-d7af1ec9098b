{"info": {"_postman_id": "924b9006-1efd-4bb4-8d76-e9d411f5450d", "name": "19 aug oms", "description": "Complete API collection for Rozana Order Management System (OMS)", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "45698663"}, "item": [{"name": "Health Check", "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/health", "host": ["{{baseUrl}}"], "path": ["health"]}, "description": "Check the health status of the OMS service"}, "response": []}]}, {"name": "APP APIs (Firebase App Token)", "item": [{"name": "Get All Orders", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Authorization", "value": "{{app_token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/app/v1/orders", "host": ["{{baseUrl}}"], "path": ["app", "v1", "orders"], "query": [{"key": "sort_order", "value": "desc", "disabled": true}, {"key": "page_size", "value": "99", "disabled": true}, {"key": "page", "value": "10", "disabled": true}]}, "description": "Get all orders for the authenticated mobile app user"}, "response": []}, {"name": "Get Order Details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{app_token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/app/v1/order_details?order_id={{sample_order_id}}", "host": ["{{baseUrl}}"], "path": ["app", "v1", "order_details"], "query": [{"key": "order_id", "value": "{{sample_order_id}}"}]}, "description": "Get detailed information about a specific order"}, "response": []}, {"name": "Order Again", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{app_token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/app/v1/order_again?limit=20&offset=0", "host": ["{{baseUrl}}"], "path": ["app", "v1", "order_again"], "query": [{"key": "limit", "value": "20"}, {"key": "offset", "value": "0"}]}, "description": "Get products that the user has ordered before for easy reordering"}, "response": []}, {"name": "Create Order", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Authorization", "value": "{{app_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"customer_id\": \"{{customer_id}}\",\n  \"customer_name\": \"John Test\",\n  \"facility_id\": \"FAC-001\",\n  \"facility_name\": \"ROZANA_TEST_WH1\",\n  \"total_amount\": 87,\n  \"payment\": [\n    {\n      \"payment_mode\": \"cod\",\n      \"create_payment_order\": false,\n      \"amount\": 87\n    }\n  ],\n  \"items\": [\n    {\n      \"sku\": \"ROZ16863-1PCS\",\n      \"quantity\": 1,\n      \"unit_price\": 39,\n      \"sale_price\": 39\n    },\n    {\n      \"sku\": \"ROZ3898-1PCS\",\n      \"quantity\": 1,\n      \"unit_price\": 48,\n      \"sale_price\": 48\n    }\n  ],\n  \"address\": {\n    \"full_name\": \"John Test\",\n    \"phone_number\": \"9110345323\",\n    \"address_line1\": \"Test Address\",\n    \"city\": \"Mumbai\",\n    \"state\": \"Maharashtra\",\n    \"postal_code\": \"400001\",\n    \"country\": \"india\",\n    \"type_of_address\": \"home\",\n    \"longitude\": 72.8777,\n    \"latitude\": 19.076\n  }\n}"}, "url": {"raw": "{{baseUrl}}/app/v1/create_order", "host": ["{{baseUrl}}"], "path": ["app", "v1", "create_order"]}, "description": "Create a new order via mobile app"}, "response": []}, {"name": "Encrypt Customer Code", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{app_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"customer_code\": \"CUST123\"\n}"}, "url": {"raw": "{{baseUrl}}/app/v1/encrypt_customer_code", "host": ["{{baseUrl}}"], "path": ["app", "v1", "encrypt_customer_code"]}, "description": "Encrypt customer code using AES encryption"}, "response": []}, {"name": "Update Order Status", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{app_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"order_id\": \"{{sample_order_id}}\",\n  \"status\": \"confirmed\"\n}"}, "url": {"raw": "{{baseUrl}}/app/v1/update_order_status", "host": ["{{baseUrl}}"], "path": ["app", "v1", "update_order_status"]}, "description": "Update the status of an order"}, "response": []}, {"name": "Update Item Status", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{app_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"order_id\": \"{{sample_order_id}}\",\n  \"sku\": \"ROZ13792-40-COTTONBLEND-CORE-1-DAILYWEAR\",\n  \"status\": \"confirmed\"\n}"}, "url": {"raw": "{{baseUrl}}/app/v1/update_item_status", "host": ["{{baseUrl}}"], "path": ["app", "v1", "update_item_status"]}, "description": "Update the status of a specific item in an order"}, "response": []}, {"name": "Cancel Order", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{app_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"order_id\": \"{{sample_order_id}}\"\n}"}, "url": {"raw": "{{baseUrl}}/app/v1/cancel_order", "host": ["{{baseUrl}}"], "path": ["app", "v1", "cancel_order"]}, "description": "Cancel an existing order"}, "response": []}, {"name": "Return Items", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{app_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"order_id\": \"{{sample_order_id}}\",\n  \"items\": [\n    {\n      \"sku\": \"ROZ13792-40-COTTONBLEND-CORE-1-DAILYWEAR\",\n      \"quantity\": 1\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/app/v1/return_items", "host": ["{{baseUrl}}"], "path": ["app", "v1", "return_items"]}, "description": "Return specific items from an order"}, "response": []}, {"name": "Return Full Order", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{app_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"order_id\": \"{{sample_order_id}}\"\n}"}, "url": {"raw": "{{baseUrl}}/app/v1/return_full_order", "host": ["{{baseUrl}}"], "path": ["app", "v1", "return_full_order"]}, "description": "Return all items in an order"}, "response": []}]}, {"name": "Payment APIs", "item": [{"name": "Create Payment Order", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{app_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"order_id\": \"{{sample_order_id}}\",\n  \"amount\": 299.99,\n  \"customer_id\": \"{{customer_id}}\",\n  \"customer_name\": \"<PERSON>\",\n  \"customer_email\": \"<EMAIL>\",\n  \"customer_phone\": \"+91-9876543210\",\n  \"notes\": {\n    \"description\": \"Payment for order {{sample_order_id}}\"\n  }\n}"}, "url": {"raw": "{{baseUrl}}/app/v1/create_payment_order", "host": ["{{baseUrl}}"], "path": ["app", "v1", "create_payment_order"]}, "description": "Create a Razorpay payment order for processing"}, "response": []}, {"name": "Verify Payment", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{app_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"oms_order_id\": \"{{sample_order_id}}\",\n  \"razorpay_order_id\": \"order_sample123\",\n  \"razorpay_payment_id\": \"pay_sample123\",\n  \"razorpay_signature\": \"sample_signature_hash\"\n}"}, "url": {"raw": "{{baseUrl}}/app/v1/verify_payment", "host": ["{{baseUrl}}"], "path": ["app", "v1", "verify_payment"]}, "description": "Verify payment signature and update order status"}, "response": []}, {"name": "Get Payment Status", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{app_token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/app/v1/payment_status/{{sample_order_id}}", "host": ["{{baseUrl}}"], "path": ["app", "v1", "payment_status", "{{sample_order_id}}"]}, "description": "Get payment status for a specific order"}, "response": []}]}, {"name": "POS APIs (Firebase POS Token)", "item": [{"name": "Get All Orders", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{pos_token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/pos/v1/orders?facility_name=ROZANA_TEST_WH1&sort_order=desc&page=1", "host": ["{{baseUrl}}"], "path": ["pos", "v1", "orders"], "query": [{"key": "facility_name", "value": "ROZANA_TEST_WH1"}, {"key": "sort_order", "value": "desc"}, {"key": "page_size", "value": "99", "disabled": true}, {"key": "page", "value": "1"}]}, "description": "Get all orders for a specific facility via POS"}, "response": []}, {"name": "Get Order Details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{pos_token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/pos/v1/order_details?order_id={{sample_order_id}}&facility_name=ROZANA_TEST_WH1", "host": ["{{baseUrl}}"], "path": ["pos", "v1", "order_details"], "query": [{"key": "order_id", "value": "{{sample_order_id}}"}, {"key": "facility_name", "value": "ROZANA_TEST_WH1"}]}, "description": "Get detailed information about a specific order via POS"}, "response": []}, {"name": "Create Order", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{pos_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"customer_id\": \"{{customer_id}}\",\n  \"customer_name\": \"John POS Test\",\n  \"facility_id\": \"FAC-001\",\n  \"facility_name\": \"ROZANA_TEST_WH1\",\n  \"total_amount\": 39,\n  \"payment\": [\n    {\n      \"payment_mode\": \"cash\",\n      \"create_payment_order\": false,\n      \"amount\": 39\n    }\n  ],\n  \"items\": [\n    {\n      \"sku\": \"ROZ16863-1PCS\",\n      \"quantity\": 1,\n      \"unit_price\": 39,\n      \"sale_price\": 39\n    },\n    {\n      \"sku\": \"ROZ3898-1PCS\",\n      \"quantity\": 1,\n      \"unit_price\": 48,\n      \"sale_price\": 48\n    }\n  ],\n  \"address\": {\n    \"full_name\": \"John POS Test\",\n    \"phone_number\": \"+91-9876543210\",\n    \"address_line1\": \"POS Test Address\",\n    \"city\": \"Mumbai\",\n    \"state\": \"Maharashtra\",\n    \"postal_code\": \"400001\",\n    \"country\": \"india\",\n    \"type_of_address\": \"home\",\n    \"longitude\": 72.8777,\n    \"latitude\": 19.076\n  }\n}"}, "url": {"raw": "{{baseUrl}}/pos/v1/create_order", "host": ["{{baseUrl}}"], "path": ["pos", "v1", "create_order"]}, "description": "Create a new order via POS system"}, "response": []}, {"name": "Update Order Status", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{pos_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"order_id\": \"{{sample_order_id}}\",\n  \"status\": \"confirmed\"\n}"}, "url": {"raw": "{{baseUrl}}/pos/v1/update_order_status", "host": ["{{baseUrl}}"], "path": ["pos", "v1", "update_order_status"]}, "description": "Update the status of an order via POS"}, "response": []}, {"name": "Update Item Status", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "{{pos_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"order_id\": \"{{sample_order_id}}\",\n  \"sku\": \"ROZ13792-40-COTTONBLEND-CORE-1-DAILYWEAR\",\n  \"status\": \"confirmed\"\n}"}, "url": {"raw": "{{baseUrl}}/pos/v1/update_item_status", "host": ["{{baseUrl}}"], "path": ["pos", "v1", "update_item_status"]}, "description": "Update the status of a specific item via POS"}, "response": []}]}, {"name": "API Token Validation Endpoints", "item": [{"name": "Get Orders (Token Validation)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{app_token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/v1/get_orders?customer_id={{customer_id}}&limit=20&offset=0&sort_order=desc", "host": ["{{baseUrl}}"], "path": ["api", "v1", "get_orders"], "query": [{"key": "customer_id", "value": "{{customer_id}}"}, {"key": "limit", "value": "20"}, {"key": "offset", "value": "0"}, {"key": "sort_order", "value": "desc"}]}, "description": "Get orders after token validation (requires external token validation service)"}, "response": []}, {"name": "Get Order Items", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{app_token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/v1/order_items?customer_id={{customer_id}}&order_id={{sample_order_id}}", "host": ["{{baseUrl}}"], "path": ["api", "v1", "order_items"], "query": [{"key": "customer_id", "value": "{{customer_id}}"}, {"key": "order_id", "value": "{{sample_order_id}}"}]}, "description": "Get order items after token validation"}, "response": []}, {"name": "Cancel Order (Token Validation)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{app_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"customer_id\": \"{{customer_id}}\",\n  \"order_id\": \"{{sample_order_id}}\"\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/cancel_order", "host": ["{{baseUrl}}"], "path": ["api", "v1", "cancel_order"]}, "description": "Cancel order after token validation"}, "response": []}, {"name": "Return Items (Token Validation)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{app_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"customer_id\": \"{{customer_id}}\",\n  \"order_id\": \"{{sample_order_id}}\",\n  \"items\": [\n    {\n      \"sku\": \"ROZ13792-40-COTTONBLEND-CORE-1-DAILYWEAR\",\n      \"quantity\": 1\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/return_items", "host": ["{{baseUrl}}"], "path": ["api", "v1", "return_items"]}, "description": "Return items after token validation"}, "response": []}, {"name": "Return Full Order (Token Validation)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{app_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"customer_id\": \"{{customer_id}}\",\n  \"order_id\": \"{{sample_order_id}}\"\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/return_full_order", "host": ["{{baseUrl}}"], "path": ["api", "v1", "return_full_order"]}, "description": "Return full order after token validation"}, "response": []}], "description": "These endpoints require external token validation service"}, {"name": "Webhook APIs", "item": [{"name": "Razorpay Webhook", "request": {"method": "POST", "header": [{"key": "X-Razorpay-Signature", "value": "sample_webhook_signature", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"event\": \"payment.captured\",\n  \"payload\": {\n    \"payment\": {\n      \"entity\": {\n        \"id\": \"pay_sample123\",\n        \"amount\": 29999,\n        \"status\": \"captured\",\n        \"notes\": {\n          \"oms_order_id\": \"{{sample_order_id}}\"\n        }\n      }\n    }\n  }\n}"}, "url": {"raw": "{{baseUrl}}/webhooks/v1/razorpay_webhook", "host": ["{{baseUrl}}"], "path": ["webhooks", "v1", "razorpay_webhook"]}, "description": "Razorpay webhook for payment status updates"}, "response": []}]}, {"name": "auth-app", "request": {"method": "POST", "header": [{"key": "accept", "value": "*/*"}, {"key": "accept-language", "value": "en-GB,en-US;q=0.9,en;q=0.8"}, {"key": "content-type", "value": "application/x-www-form-urlencoded"}, {"key": "origin", "value": "https://web-dev.rozana.tech"}, {"key": "priority", "value": "u=1, i"}, {"key": "referer", "value": "https://web-dev.rozana.tech/"}, {"key": "sec-ch-ua", "value": "\"Not;A=Brand\";v=\"99\", \"Google Chrome\";v=\"139\", \"Chromium\";v=\"139\""}, {"key": "sec-ch-ua-mobile", "value": "?1"}, {"key": "sec-ch-ua-platform", "value": "\"Android\""}, {"key": "sec-fetch-dest", "value": "empty"}, {"key": "sec-fetch-mode", "value": "cors"}, {"key": "sec-fetch-site", "value": "cross-site"}, {"key": "user-agent", "value": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"}, {"key": "x-browser-channel", "value": "stable"}, {"key": "x-browser-copyright", "value": "Copyright 2025 Google LLC. All rights reserved."}, {"key": "x-browser-validation", "value": "Hg4L+ikvx4e+Kz4C1Vi1rALvggw="}, {"key": "x-browser-year", "value": "2025"}, {"key": "", "value": "eHiAsZAzwuQV82rcsKBHWYmd5oQ2", "disabled": true}, {"key": "x-client-version", "value": "Chrome/JsCore/11.9.1/FirebaseCore-web"}, {"key": "x-firebase-gmpid", "value": "1:211080965257:web:3b917158271e9aa422aa58"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "grant_type", "value": "refresh_token", "type": "text"}, {"key": "refresh_token", "value": "AMf-vBwuDcyi3Uyhhx1u9QuEJtnnRgcMC3x06S9WRFSVB8R3DLZDP36-ezs01zEa34pg7wdRC6D_1N2-mmdKwx6Hm53te4-yLPBQhwo0cPZPje6IPRj96DEFxcjVP8NhN982COAvQc7YjCNkFIxHgky2rkRxvrGWlX5GDPdAyc43aH6o2OFkgKwyFt08dq1XrJrepQGevu0h27Cg8rZJJsuXtuRGFCHeJeoSn4stYT9BBSfMELG1s5U", "type": "text"}]}, "url": {"raw": "https://securetoken.googleapis.com/v1/token?key=AIzaSyBEEWROSUbSLVl9T3HHSp8VdgsU6oTrdFI", "protocol": "https", "host": ["securetoken", "googlea<PERSON>", "com"], "path": ["v1", "token"], "query": [{"key": "key", "value": "AIzaSyBEEWROSUbSLVl9T3HHSp8VdgsU6oTrdFI"}]}}, "response": []}, {"name": "auth-pos", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"wZVWpnSIpY\",\n    \"returnSecureToken\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword?key=AIzaSyC_fE1A0JapukDalgCdJix4mmoVWx-K-2g", "protocol": "https", "host": ["identitytoolkit", "googlea<PERSON>", "com"], "path": ["v1", "accounts:signInWithPassword"], "query": [{"key": "key", "value": "AIzaSyC_fE1A0JapukDalgCdJix4mmoVWx-K-2g"}]}}, "response": []}], "variable": [{"key": "baseUrl", "value": "http://localhost:8000", "type": "string"}, {"key": "app_token", "value": "eyJhbGciOiJSUzI1NiIsImtpZCI6IjU3YmZiMmExMWRkZmZjMGFkMmU2ODE0YzY4NzYzYjhjNjg3NTgxZDgiLCJ0eXAiOiJKV1QifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.H_b37qh0_m9fDf4Py6V3YKnFj9EfJw0e5eo3j4XR7WHmlLLSgmnXEmK5030_qdNQry2slPy3Y6Z7IGcpDyBxKv9vexFLeSFQ21PzmVPb1c6gZWou4lBqQCgrrvN1PqOlbPNF7tzkpERO6uBZQuiNhealb0oX0elcTnfD9kR4Ms_rdqk5OD8LZyokIV4XEzImzgERD00c87PUIwzA4oHobUQzo977M9omN3UeSbo8WVzcG7nAZDhE-sVaOQi49HJhShwlrvgKoeHyxkquucESolalpuMoxTjF2qiSFd-XWtRh78v1SMh2hQm_3DTIybR_WzQ5DndaOdCarfjnVQAJuw", "type": "string"}, {"key": "pos_token", "value": "eyJhbGciOiJSUzI1NiIsImtpZCI6IjU3YmZiMmExMWRkZmZjMGFkMmU2ODE0YzY4NzYzYjhjNjg3NTgxZDgiLCJ0eXAiOiJKV1QifQ.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.iTkGQm9iOOWkYHL4D-8BmzLk1g8EyRuXLey9ZS3JwL6HRwWPhjU6u9tczGRhz6oixAQDRfIOJiR_bL_82IL9-Oe5B13sbfopjeGNw5lDJP8z1-Avxlg2szIohvnnL2MvUfT8rFnSkl_8sM9-K9725uBYwG22uLBRiZuvW-wNngaALzKXLSDYaqRTZC1WjiMilrO7_KQlOQNJ1de3rLNgVf2D8ZMlYbMXYH2srqG21AstHqz72H8x-M2anqlHybvrMGn0WVHVW4-qvil7_Ru2eaR5B5CuSDlAALDYdUhx5Yfbz7N7XMPsVUolFQIoNssh4OJxy9S-WLEZvLqk_aknlA", "type": "string"}, {"key": "customer_id", "value": "2CN3aYJnaGXpaguuctWAubZnKKp1", "type": "string"}, {"key": "sample_order_id", "value": "YDPM1", "type": "string"}]}