# Updated OMS Service API Testing Instructions

## Prerequisites
- <PERSON><PERSON> and Docker Compose installed
- Postman or similar API testing tool

## 1. Environment Setup

### Create .env file
Copy the `.env.sample` to `.env` and configure:

```bash
cp .env.sample .env
```

### Basic .env configuration for testing:
```bash
DATABASE_URL=***********************************/oms_db
DATABASE_READ_URL=***********************************/oms_db
ALEMBIC_DATABASE_URL=postgresql+asyncpg://user:password@db1:5432/oms_db

DEBUG=true
STOCK_CHECK_ENABLED=false
OTEL_SDK_DISABLED=true

# Optional integrations (can be disabled for basic testing)
POTIONS_INTEGRATION_ENABLED=false
RAZORPAY_INTEGRATION_ENABLED=false

# CORS settings
ALLOWED_ORIGINS=*
```

## 2. Running the Service

### Start Docker Environment
```bash
# Build and start all services
docker-compose up -d

# Check service health
curl http://localhost:8000/health

# Check logs if needed
docker-compose logs -f app
```

### Run Database Migrations
```bash
# Enter the app container
docker exec -it oms-api bash

# Run migrations
cd /application && alembic upgrade head
```

## 3. Authentication Setup

### Firebase App Token (for /app/v1/* endpoints)
1. Use the `auth-app` request in Postman collection
2. Copy the `idToken` from response
3. Set as `app_token` variable in Postman (without "Bearer" prefix)

### Firebase POS Token (for /pos/v1/* endpoints)
1. Use the `auth-pos` request in Postman collection 
2. Copy the `idToken` from response
3. Set as `pos_token` variable in Postman (without "Bearer" prefix)

## 4. Testing Order Flow

### Basic Order Testing Sequence:
1. **Health Check**: `/health`
2. **Create Order**: `/app/v1/create_order` (app) or `/pos/v1/create_order` (pos)
3. **Get Orders**: `/app/v1/orders` or `/pos/v1/orders`
4. **Get Order Details**: `/app/v1/order_details` or `/pos/v1/order_details`
5. **Update Order Status**: `/app/v1/update_order_status`
6. **Update Item Status**: `/app/v1/update_item_status`

### Payment Testing (if Razorpay enabled):
1. **Create Payment Order**: `/app/v1/create_payment_order`
2. **Verify Payment**: `/app/v1/verify_payment`
3. **Check Payment Status**: `/app/v1/payment_status/{order_id}`

### Returns Testing:
1. **Create Return**: `/app/v1/create_return`
2. **Return Items** (API validation): `/api/v1/return_items`
3. **Return Full Order** (API validation): `/api/v1/return_full_order`

## 5. Environment Variables for Testing

### Required Variables:
- `baseUrl`: http://localhost:8000
- `app_token`: [Get from auth-app response]
- `pos_token`: [Get from auth-pos response]
- `customer_id`: Any valid customer ID
- `sample_order_id`: Order ID from created order

## 6. Troubleshooting

### Common Issues:
1. **401 Unauthorized**: Check token validity and format (no "Bearer" prefix for app/pos tokens)
2. **404 Order Not Found**: Ensure order_id exists and is accessible by the authenticated user
3. **500 Internal Server Error**: Check Docker logs: `docker-compose logs app`

### Checking Service Status:
```bash
# Check if containers are running
docker-compose ps

# Check app logs
docker-compose logs app

# Check database connection
docker-compose logs db1

# Restart services if needed
docker-compose restart
```

## 7. Endpoint Summary

### App Routes (Firebase App Token)
- `GET /app/v1/orders` - Get all orders for user
- `GET /app/v1/order_details` - Get specific order
- `GET /app/v1/order_again` - Get order again products
- `POST /app/v1/create_order` - Create new order
- `POST /app/v1/encrypt_customer_code` - Encrypt customer code
- `PUT/PATCH /app/v1/update_order_status` - Update order status
- `PUT/PATCH /app/v1/update_item_status` - Update item status
- `POST /app/v1/cancel_order` - Cancel order
- `POST /app/v1/create_return` - Create return

### POS Routes (Firebase POS Token)
- `GET /pos/v1/orders` - Get facility orders
- `GET /pos/v1/order_details` - Get specific order
- `POST /pos/v1/create_order` - Create POS order
- `PUT/PATCH /pos/v1/update_order_status` - Update order status
- `PUT/PATCH /pos/v1/update_item_status` - Update item status

### Payment Routes (Firebase App Token)
- `POST /app/v1/create_payment_order` - Create Razorpay order
- `POST /app/v1/verify_payment` - Verify payment
- `GET /app/v1/payment_status/{order_id}` - Get payment status

### API Token Validation Routes (Bearer Token)
- `GET /api/v1/get_orders` - Get orders with external validation
- `GET /api/v1/order_details` - Get order details with validation
- `GET /api/v1/order_items` - Get order items with validation
- `POST /api/v1/cancel_order` - Cancel order with validation
- `POST /api/v1/return_items` - Return items with validation
- `POST /api/v1/return_full_order` - Return full order with validation

### Webhook Routes
- `POST /webhooks/v1/razorpay_webhook` - Razorpay payment webhooks
