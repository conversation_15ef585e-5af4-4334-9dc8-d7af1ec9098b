#!/usr/bin/env python3
"""
Quick test script to verify the TypeError fix in order creation flow
"""

import asyncio
import sys
import os

# Add the application directory to Python path
sys.path.insert(0, '/Users/<USER>/Desktop/ROZANA/rozana-oms-service/application')

from app.core.order_functions import create_payment_records_for_order

async def test_function_signature():
    """Test that the create_payment_records_for_order function accepts the correct parameters"""
    
    # Mock data matching the expected function signature
    test_y20acb421 = 123  # int
    test_order_external_id = "OMS-TEST-001"  # str
    test_payments = []  # Empty list for quick test
    test_total_amount = 100.0  # float
    test_order_data = {"customer_id": "test123", "customer_name": "Test User"}  # dict
    
    try:
        # Try calling the function with the parameters that were causing the TypeError
        result = await create_payment_records_for_order(
            y20acb421=test_y20acb421,  # This should work (not internal_y20acb421)
            order_external_id=test_order_external_id,
            payments=test_payments,
            total_amount=test_total_amount,
            order_data=test_order_data
        )
        print("✅ Function call succeeded - parameter signature is correct!")
        print(f"Result: {result}")
        return True
        
    except TypeError as e:
        print(f"❌ TypeError still exists: {e}")
        return False
    except Exception as e:
        print(f"⚠️  Function call succeeded but other error occurred: {e}")
        print("This is expected since we're not providing real data - the important thing is no TypeError")
        return True

if __name__ == "__main__":
    print("Testing create_payment_records_for_order function signature...")
    result = asyncio.run(test_function_signature())
    
    if result:
        print("\n🎉 SUCCESS: The TypeError has been fixed!")
        print("The function now accepts the correct parameter names.")
    else:
        print("\n💥 FAILED: TypeError still exists in the function signature.")
        
    sys.exit(0 if result else 1)
